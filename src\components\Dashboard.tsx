import React, { useState, useEffect } from 'react';
import { User, LogOut, Settings, Camera, Users, Briefcase, Search, Bell, Plus, Menu, X, Home, MessageSquare, Calendar, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface DashboardProps {
  user: SupabaseUser;
}

type UserRole = 'model' | 'talent' | 'agency' | 'admin';

export function Dashboard({ user }: DashboardProps) {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'overview' | 'portfolio' | 'castings' | 'profile' | 'messages' | 'calendar' | 'analytics'>('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userRole, setUserRole] = useState<UserRole>('model'); // This would come from user data in real app

  useEffect(() => {
    // In a real app, you'd fetch user role from your database
    // For now, we'll determine based on email or other factors
    setUserRole('model'); // Default to model
  }, [user]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  const getNavigationItems = () => {
    const baseItems = [
      { id: 'overview', label: 'Overview', icon: Home },
      { id: 'messages', label: 'Messages', icon: MessageSquare },
      { id: 'calendar', label: 'Calendar', icon: Calendar },
      { id: 'profile', label: 'Profile', icon: User },
    ];

    if (userRole === 'model' || userRole === 'talent') {
      return [
        ...baseItems.slice(0, 1),
        { id: 'portfolio', label: 'Portfolio', icon: Camera },
        { id: 'castings', label: 'Castings', icon: Search },
        ...baseItems.slice(1),
      ];
    } else if (userRole === 'agency') {
      return [
        ...baseItems.slice(0, 1),
        { id: 'talent', label: 'Talent', icon: Users },
        { id: 'castings', label: 'Castings', icon: Briefcase },
        { id: 'analytics', label: 'Analytics', icon: TrendingUp },
        ...baseItems.slice(1),
      ];
    }

    return baseItems;
  };

  const renderSidebar = () => (
    <div className={`
      fixed inset-y-0 left-0 z-50 w-64 bg-zinc-900 transform transition-transform duration-300 ease-in-out
      lg:translate-x-0 lg:static lg:inset-0
      ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
    `}>
      {/* Logo/Brand */}
      <div className="flex items-center justify-between p-6 border-b border-zinc-700">
        <h1 className="text-white text-xl font-bold">ModelHub</h1>
        <button
          onClick={() => setSidebarOpen(false)}
          className="lg:hidden text-zinc-400 hover:text-white"
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {getNavigationItems().map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.id}>
                <button
                  onClick={() => {
                    setActiveTab(item.id as any);
                    setSidebarOpen(false);
                  }}
                  className={`w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === item.id
                      ? 'bg-white text-black'
                      : 'text-zinc-300 hover:bg-zinc-800 hover:text-white'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.label}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User section */}
      <div className="p-4 border-t border-zinc-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-white text-sm font-medium">
                {user.email?.split('@')[0] || 'User'}
              </p>
              <p className="text-zinc-400 text-xs capitalize">{userRole}</p>
            </div>
          </div>
          <button
            onClick={handleSignOut}
            className="text-zinc-400 hover:text-white transition-colors"
          >
            <LogOut className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  // Overlay for mobile sidebar
  const renderOverlay = () => (
    sidebarOpen && (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        onClick={() => setSidebarOpen(false)}
      />
    )
  );

  const renderTopBar = () => (
    <div className="bg-white border-b border-zinc-200 px-4 lg:px-6 py-4 flex items-center justify-between">
      <div className="flex items-center">
        <button
          onClick={() => setSidebarOpen(true)}
          className="lg:hidden p-2 text-gray-400 hover:text-gray-600 transition-colors mr-4"
        >
          <Menu className="w-6 h-6" />
        </button>
        <h2 className="text-xl lg:text-2xl font-semibold text-gray-900 capitalize">
          {activeTab}
        </h2>
      </div>
      <div className="flex items-center space-x-2 lg:space-x-4">
        <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors relative">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </button>
        <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
          <Settings className="w-5 h-5" />
        </button>
        <div className="lg:hidden">
          <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="p-4 lg:p-6">
            {/* Welcome Section */}
            <div className="mb-8">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                Welcome back, {user.email?.split('@')[0]}!
              </h1>
              <p className="text-gray-600">
                Here's what's happening with your {userRole} profile today.
              </p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-4 lg:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {userRole === 'agency' ? 'Talent Managed' : 'Portfolio Views'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {userRole === 'agency' ? '47' : '1,234'}
                    </p>
                    <p className="text-xs text-green-600 mt-1">+12% from last month</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-full">
                    {userRole === 'agency' ? (
                      <Users className="w-6 h-6 text-blue-500" />
                    ) : (
                      <Camera className="w-6 h-6 text-blue-500" />
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-4 lg:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {userRole === 'agency' ? 'Active Campaigns' : 'Active Applications'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {userRole === 'agency' ? '12' : '8'}
                    </p>
                    <p className="text-xs text-green-600 mt-1">+3 this week</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-full">
                    <Search className="w-6 h-6 text-green-500" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-4 lg:p-6 sm:col-span-2 lg:col-span-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Profile Completion</p>
                    <p className="text-2xl font-bold text-gray-900">85%</p>
                    <p className="text-xs text-orange-600 mt-1">Add more photos</p>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-full">
                    <User className="w-6 h-6 text-purple-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-4 lg:p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <button className="text-sm text-blue-600 hover:text-blue-800">View all</button>
              </div>
              <div className="space-y-4">
                {[
                  {
                    title: userRole === 'agency' ? 'New talent application' : 'New casting opportunity',
                    description: userRole === 'agency' ? 'Sarah Johnson applied for Fashion Model position' : 'Fashion shoot for summer collection',
                    time: '2 hours ago',
                    type: 'success'
                  },
                  {
                    title: userRole === 'agency' ? 'Campaign completed' : 'Portfolio viewed',
                    description: userRole === 'agency' ? 'Summer Collection campaign wrapped successfully' : 'Your portfolio was viewed by Elite Models',
                    time: '5 hours ago',
                    type: 'info'
                  },
                  {
                    title: 'Profile updated',
                    description: userRole === 'agency' ? 'Company information updated' : 'You updated your measurements',
                    time: '1 day ago',
                    type: 'neutral'
                  }
                ].map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 py-3 border-b border-zinc-100 last:border-b-0">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'success' ? 'bg-green-500' :
                      activity.type === 'info' ? 'bg-blue-500' : 'bg-gray-400'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">{activity.title}</p>
                      <p className="text-sm text-gray-600 truncate">{activity.description}</p>
                    </div>
                    <span className="text-sm text-gray-500 whitespace-nowrap">{activity.time}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-4 lg:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                {userRole === 'agency' ? [
                  { label: 'Post Casting', icon: Plus, action: () => setActiveTab('castings') },
                  { label: 'Find Talent', icon: Search, action: () => setActiveTab('talent') },
                  { label: 'View Analytics', icon: TrendingUp, action: () => setActiveTab('analytics') },
                  { label: 'Messages', icon: MessageSquare, action: () => setActiveTab('messages') }
                ] : [
                  { label: 'Upload Photos', icon: Plus, action: () => setActiveTab('portfolio') },
                  { label: 'Browse Castings', icon: Search, action: () => setActiveTab('castings') },
                  { label: 'Update Profile', icon: User, action: () => setActiveTab('profile') },
                  { label: 'Messages', icon: MessageSquare, action: () => setActiveTab('messages') }
                ].map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <button
                      key={index}
                      onClick={action.action}
                      className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Icon className="w-6 h-6 text-gray-600 mb-2" />
                      <span className="text-sm font-medium text-gray-900">{action.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        );

      case 'portfolio':
        return (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">My Portfolio</h3>
              <button className="flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                <Plus className="w-4 h-4 mr-2" />
                Add Photos
              </button>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="aspect-square bg-zinc-200 rounded-lg flex items-center justify-center">
                  <Camera className="w-8 h-8 text-zinc-400" />
                </div>
              ))}
              <div className="aspect-square border-2 border-dashed border-zinc-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-zinc-400 transition-colors">
                <div className="text-center">
                  <Plus className="w-8 h-8 text-zinc-400 mx-auto mb-2" />
                  <p className="text-sm text-zinc-600">Add Photo</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'castings':
        return (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Available Castings</h3>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder="Search castings..."
                  className="px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                />
                <button className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                  Search
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        Summer Fashion Campaign
                      </h4>
                      <p className="text-gray-600 mb-4">
                        Looking for models for an upcoming summer fashion campaign. 
                        Must be available for 3-day shoot in Los Angeles.
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>📍 Los Angeles, CA</span>
                        <span>💰 $2,000 - $5,000</span>
                        <span>📅 June 25-27</span>
                      </div>
                    </div>
                    <button className="ml-4 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                      Apply
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'profile':
        return (
          <div className="p-6">
            <div className="max-w-2xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Profile Settings</h3>
              
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={user.email || ''}
                      disabled
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Display Name
                    </label>
                    <input
                      type="text"
                      placeholder="Enter your display name"
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bio
                    </label>
                    <textarea
                      rows={4}
                      placeholder="Tell us about yourself..."
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>

                  <button className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {renderOverlay()}
      {renderSidebar()}
      <div className="flex-1 lg:ml-64">
        {renderTopBar()}
        <main className="flex-1 overflow-y-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
