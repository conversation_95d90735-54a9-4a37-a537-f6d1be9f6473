import React, { useState } from 'react';
import { User, LogOut, Settings, Camera, Users, Briefcase, Search, Bell, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface DashboardProps {
  user: SupabaseUser;
}

export function Dashboard({ user }: DashboardProps) {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'overview' | 'portfolio' | 'castings' | 'profile'>('overview');

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  const renderSidebar = () => (
    <div className="w-64 bg-zinc-900 h-screen fixed left-0 top-0 flex flex-col">
      {/* Logo/Brand */}
      <div className="p-6 border-b border-zinc-700">
        <h1 className="text-white text-xl font-bold">ModelHub</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          <li>
            <button
              onClick={() => setActiveTab('overview')}
              className={`w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors ${
                activeTab === 'overview' 
                  ? 'bg-white text-black' 
                  : 'text-zinc-300 hover:bg-zinc-800 hover:text-white'
              }`}
            >
              <Briefcase className="w-5 h-5 mr-3" />
              Overview
            </button>
          </li>
          <li>
            <button
              onClick={() => setActiveTab('portfolio')}
              className={`w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors ${
                activeTab === 'portfolio' 
                  ? 'bg-white text-black' 
                  : 'text-zinc-300 hover:bg-zinc-800 hover:text-white'
              }`}
            >
              <Camera className="w-5 h-5 mr-3" />
              Portfolio
            </button>
          </li>
          <li>
            <button
              onClick={() => setActiveTab('castings')}
              className={`w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors ${
                activeTab === 'castings' 
                  ? 'bg-white text-black' 
                  : 'text-zinc-300 hover:bg-zinc-800 hover:text-white'
              }`}
            >
              <Search className="w-5 h-5 mr-3" />
              Castings
            </button>
          </li>
          <li>
            <button
              onClick={() => setActiveTab('profile')}
              className={`w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors ${
                activeTab === 'profile' 
                  ? 'bg-white text-black' 
                  : 'text-zinc-300 hover:bg-zinc-800 hover:text-white'
              }`}
            >
              <User className="w-5 h-5 mr-3" />
              Profile
            </button>
          </li>
        </ul>
      </nav>

      {/* User section */}
      <div className="p-4 border-t border-zinc-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-white text-sm font-medium">
                {user.email?.split('@')[0] || 'User'}
              </p>
              <p className="text-zinc-400 text-xs">{user.email}</p>
            </div>
          </div>
          <button
            onClick={handleSignOut}
            className="text-zinc-400 hover:text-white transition-colors"
          >
            <LogOut className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const renderTopBar = () => (
    <div className="bg-white border-b border-zinc-200 px-6 py-4 flex items-center justify-between">
      <div className="flex items-center">
        <h2 className="text-2xl font-semibold text-gray-900 capitalize">
          {activeTab}
        </h2>
      </div>
      <div className="flex items-center space-x-4">
        <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
          <Bell className="w-5 h-5" />
        </button>
        <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
          <Settings className="w-5 h-5" />
        </button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Portfolio Views</p>
                    <p className="text-2xl font-bold text-gray-900">1,234</p>
                  </div>
                  <Camera className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Castings</p>
                    <p className="text-2xl font-bold text-gray-900">8</p>
                  </div>
                  <Search className="w-8 h-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Profile Completion</p>
                    <p className="text-2xl font-bold text-gray-900">85%</p>
                  </div>
                  <User className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b border-zinc-100">
                  <div>
                    <p className="font-medium text-gray-900">New casting opportunity</p>
                    <p className="text-sm text-gray-600">Fashion shoot for summer collection</p>
                  </div>
                  <span className="text-sm text-gray-500">2 hours ago</span>
                </div>
                <div className="flex items-center justify-between py-3 border-b border-zinc-100">
                  <div>
                    <p className="font-medium text-gray-900">Portfolio viewed</p>
                    <p className="text-sm text-gray-600">Your portfolio was viewed by Elite Models</p>
                  </div>
                  <span className="text-sm text-gray-500">5 hours ago</span>
                </div>
                <div className="flex items-center justify-between py-3">
                  <div>
                    <p className="font-medium text-gray-900">Profile updated</p>
                    <p className="text-sm text-gray-600">You updated your measurements</p>
                  </div>
                  <span className="text-sm text-gray-500">1 day ago</span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'portfolio':
        return (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">My Portfolio</h3>
              <button className="flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                <Plus className="w-4 h-4 mr-2" />
                Add Photos
              </button>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="aspect-square bg-zinc-200 rounded-lg flex items-center justify-center">
                  <Camera className="w-8 h-8 text-zinc-400" />
                </div>
              ))}
              <div className="aspect-square border-2 border-dashed border-zinc-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-zinc-400 transition-colors">
                <div className="text-center">
                  <Plus className="w-8 h-8 text-zinc-400 mx-auto mb-2" />
                  <p className="text-sm text-zinc-600">Add Photo</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'castings':
        return (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Available Castings</h3>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder="Search castings..."
                  className="px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                />
                <button className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                  Search
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        Summer Fashion Campaign
                      </h4>
                      <p className="text-gray-600 mb-4">
                        Looking for models for an upcoming summer fashion campaign. 
                        Must be available for 3-day shoot in Los Angeles.
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>📍 Los Angeles, CA</span>
                        <span>💰 $2,000 - $5,000</span>
                        <span>📅 June 25-27</span>
                      </div>
                    </div>
                    <button className="ml-4 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                      Apply
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'profile':
        return (
          <div className="p-6">
            <div className="max-w-2xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Profile Settings</h3>
              
              <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={user.email || ''}
                      disabled
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Display Name
                    </label>
                    <input
                      type="text"
                      placeholder="Enter your display name"
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bio
                    </label>
                    <textarea
                      rows={4}
                      placeholder="Tell us about yourself..."
                      className="w-full px-4 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>

                  <button className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {renderSidebar()}
      <div className="flex-1 ml-64">
        {renderTopBar()}
        <main className="flex-1 overflow-y-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
