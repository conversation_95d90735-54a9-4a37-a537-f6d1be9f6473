import { createClient } from '@supabase/supabase-js';
const supabaseUrl = 'https://wyolyajdmppipqubjuuj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5b2x5YWpkbXBwaXBxdWJqdXVqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzOTc2NDMsImV4cCI6MjA2Mzk3MzY0M30.0PSQdeYXoK7b_Ww_efBZf2yqAsiP97nzHsFX2kMYUKw';
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});