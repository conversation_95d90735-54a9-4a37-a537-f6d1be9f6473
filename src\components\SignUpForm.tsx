import React, { useEffect, useState, useRef } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import gsap from 'gsap';
import { fadeInStagger, inputFocusAnimation, inputBlurAnimation, buttonHoverAnimation, buttonLeaveAnimation } from '../utils/animations';
import { ImageLoader } from './ImageLoader';
import { AuthFormInput } from './FormInput';
import { validateEmail, validatePassword, validateConfirmPassword, FormErrors } from '../utils/validation';
import { supabase } from '../lib/supabase';

export function SignUpForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [activeTab, setActiveTab] = useState<'model' | 'agent'>('model');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const formRef = useRef<HTMLDivElement>(null);
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const buttonRefs = useRef<HTMLButtonElement[]>([]);
  const textRefs = useRef<HTMLSpanElement[]>([]);
  const forgotPasswordRef = useRef<HTMLDivElement>(null);
  const labelRefs = useRef<HTMLLabelElement[]>([]);
  const iconRefs = useRef<SVGSVGElement[]>([]); // Add this new ref
  // Optimized WebP image URL
  const signUpImageUrl = 'https://pub-e5ad4e77da0042ddbcb92671e6e5a061.r2.dev/2MDLBW.webp';
  useEffect(() => {
    // Preload image
    const preloadImage = new Image();
    preloadImage.src = signUpImageUrl;
  }, []);
  useEffect(() => {
    if (formRef.current) {
      // Update elements array to include labels in the correct sequence
      const elements = [formRef.current, ...labelRefs.current, ...inputRefs.current, ...buttonRefs.current, ...iconRefs.current, forgotPasswordRef.current, ...textRefs.current].filter(Boolean) as HTMLElement[];
      // Start animation when component mounts
      fadeInStagger(elements);
    }
  }, []);
  const handleInputFocus = (element: HTMLElement) => {
    inputFocusAnimation(element);
  };
  const handleInputBlur = (element: HTMLElement) => {
    inputBlurAnimation(element);
  };
  const handleButtonHover = (element: HTMLElement) => {
    buttonHoverAnimation(element);
  };
  const handleButtonLeave = (element: HTMLElement) => {
    buttonLeaveAnimation(element);
  };
  const validateForm = (): boolean => {
    const emailValidation = validateEmail(email);
    const passwordValidation = validatePassword(password);
    const confirmPasswordValidation = validateConfirmPassword(password, confirmPassword);

    const errors: FormErrors = {};

    if (!emailValidation.isValid) {
      errors.email = emailValidation.errors[0];
    }

    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0];
    }

    if (!confirmPasswordValidation.isValid) {
      errors.confirmPassword = confirmPasswordValidation.errors[0];
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setFormErrors({});

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const {
        data,
        error
      } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });
      if (error) throw error;
      // Handle successful sign up
      console.log('Signed up:', data);
      navigate('/onboarding');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  const handleGoogleSignUp = async () => {
    try {
      setIsLoading(true);
      const {
        data,
        error
      } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          skipBrowserRedirect: false,
          redirectTo: window.location.origin
        }
      });
      if (error) {
        console.error('Google signup error:', error);
        throw error;
      }
    } catch (err) {
      console.error('Signup error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred with Google signup');
    } finally {
      setIsLoading(false);
    }
  };
  return <div className="w-full min-h-screen flex bg-zinc-950 overflow-hidden" ref={formRef}>
      {/* Left side - Image */}
      <div className="hidden lg:block lg:w-3/5 xl:w-7/12 h-screen relative">
        <ImageLoader src={signUpImageUrl} alt="Sign up background" className="w-full h-full object-cover" />
      </div>
      {/* Right side - Sign Up Form */}
      <div className="w-full lg:w-2/5 xl:w-5/12 min-h-screen bg-black flex flex-col justify-center px-6 sm:px-12 lg:px-16 xl:px-24 relative">
        {/* Tabs */}

        <div className="w-full max-w-md mx-auto">
          {/* Header */}
          <h2 className="text-zinc-400 text-base font-normal mb-12">
            Create your account
          </h2>

          {/* Email Field */}
          <div className="mb-8">
            <AuthFormInput
              ref={el => inputRefs.current[0] = el as HTMLInputElement}
              label="Email"
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              onFocus={e => handleInputFocus(e.target)}
              onBlur={e => handleInputBlur(e.target)}
              placeholder="<EMAIL>"
              error={formErrors.email}
              darkTheme={true}
            />
          </div>
          {/* Password Field */}
          <div className="mb-8">
            <AuthFormInput
              ref={el => inputRefs.current[1] = el as HTMLInputElement}
              label="Password"
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              onFocus={e => handleInputFocus(e.target)}
              onBlur={e => handleInputBlur(e.target)}
              placeholder="Enter your password here"
              error={formErrors.password}
              showPasswordToggle={true}
              showPassword={showPassword}
              onTogglePassword={() => setShowPassword(!showPassword)}
              darkTheme={true}
            />
          </div>
          {/* Confirm Password Field */}
          <div className="mb-8">
            <AuthFormInput
              ref={el => inputRefs.current[2] = el as HTMLInputElement}
              label="Confirm Password"
              type="password"
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              onFocus={e => handleInputFocus(e.target)}
              onBlur={e => handleInputBlur(e.target)}
              placeholder="Enter password confirmation here"
              error={formErrors.confirmPassword}
              showPasswordToggle={true}
              showPassword={showConfirmPassword}
              onTogglePassword={() => setShowConfirmPassword(!showConfirmPassword)}
              darkTheme={true}
            />
          </div>
          {/* Forgot Password */}
          <div className="mb-16" ref={forgotPasswordRef}>
            <span ref={el => textRefs.current[0] = el as HTMLSpanElement} className="text-stone-300 text-sm font-normal">
              Forgot your password.{' '}
            </span>
            <button ref={el => buttonRefs.current[3] = el as HTMLButtonElement} className="text-white text-sm font-bold underline hover:no-underline transition-all">
              Reset it here
            </button>
          </div>
          {/* Sign Up Button */}
          {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
          <button ref={el => buttonRefs.current[0] = el as HTMLButtonElement} onClick={handleEmailSignUp} disabled={isLoading} className="w-full h-14 bg-white rounded-full text-black text-base font-medium hover:bg-gray-100 transition-colors mb-6 disabled:opacity-50">
            {isLoading ? 'Creating account...' : 'Sign up with us'}
          </button>
          {/* Google Sign Up Button */}
          <button ref={el => buttonRefs.current[1] = el as HTMLButtonElement} onClick={handleGoogleSignUp} disabled={isLoading} className="w-full h-14 rounded-full border-2 border-zinc-400 text-stone-300 text-base font-medium hover:border-zinc-300 hover:text-white transition-colors mb-16 disabled:opacity-50">
            Sign up with Google
          </button>
          {/* Login Link */}
          <div className="text-center">
            <span className="text-zinc-400 text-sm font-normal">
              Already registered?{' '}
            </span>
            <button onClick={() => navigate('/login')} className="text-neutral-200 text-sm font-medium hover:text-white transition-colors">
              Log into your account
            </button>
          </div>
        </div>
      </div>
    </div>;
}