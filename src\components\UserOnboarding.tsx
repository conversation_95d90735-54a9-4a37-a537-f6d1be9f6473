import React, { useEffect, useState, useRef } from 'react';
import { ArrowLeft, User, Briefcase, Camera, ChevronDown, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { ProgressSteps } from './ProgressSteps';
import { FormInput, FormSelect } from './FormInput';
import { PortfolioUpload, UploadedFile } from './FileUpload';
import { validateName, validateAge, validateHeight, validateMeasurement, validateIdPassport, validateNationality, FormErrors } from '../utils/validation';
type InitialChoice = 'model-talent' | 'agent-scout' | null;
type UserType = 'aspiring-model' | 'professional-model' | 'talent' | null;
type Gender = 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
type HeightUnit = 'cm' | 'ft';
interface ModelOnboardingData {
  firstName: string;
  lastName: string;
  gender: Gender;
  dateOfBirth: string;
  nationality: string;
  idPassportNumber: string;
  height: string;
  heightUnit: HeightUnit;
  heightFeet?: string;
  heightInches?: string;
  measurements: {
    bust: string;
    waist: string;
    hips: string;
  };
  eyeColor: string;
  hairColor: string;
  experience: 'none' | 'some' | 'experienced' | 'professional';
}
const countries = ['Afghanistan', 'Albania', 'Zimbabwe'];
export function UserOnboarding() {
  const navigate = useNavigate();
  const [initialChoice, setInitialChoice] = useState<InitialChoice>(null);
  const [step, setStep] = useState(0);
  const [userType, setUserType] = useState<UserType>(null);
  const [countrySearch, setCountrySearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [portfolioFiles, setPortfolioFiles] = useState<UploadedFile[]>([]);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const containerRef = useRef<HTMLDivElement>(null);
  // Add the missing formData state
  const [formData, setFormData] = useState<ModelOnboardingData>({
    firstName: '',
    lastName: '',
    gender: 'prefer-not-to-say',
    dateOfBirth: '',
    nationality: '',
    idPassportNumber: '',
    height: '',
    heightUnit: 'cm',
    heightFeet: '',
    heightInches: '',
    measurements: {
      bust: '',
      waist: '',
      hips: ''
    },
    eyeColor: '',
    hairColor: '',
    experience: 'none'
  });
  const handleInputChange = (field: keyof ModelOnboardingData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  const [heightUnit, setHeightUnit] = useState<HeightUnit>('cm');
  const steps = {
    initial: ['Role', 'Basic Info', 'Details', 'Portfolio', 'Review'],
    'aspiring-model': ['Role', 'Basic Info', 'Measurements', 'Portfolio', 'Review'],
    'professional-model': ['Role', 'Basic Info', 'Measurements', 'Portfolio', 'Review'],
    talent: ['Role', 'Basic Info', 'Specialty', 'Portfolio', 'Review']
  };

  const getCurrentSteps = () => {
    if (userType === 'aspiring-model' || userType === 'professional-model') {
      return steps['aspiring-model'];
    }
    if (userType === 'talent') {
      return steps.talent;
    }
    return steps.initial;
  };

  const currentSteps = getCurrentSteps();

  const validateCurrentStep = (): boolean => {
    const errors: FormErrors = {};

    if (step === 1) { // Basic Info
      const firstNameValidation = validateName(formData.firstName, 'First name');
      const lastNameValidation = validateName(formData.lastName, 'Last name');
      const dobValidation = validateAge(formData.dateOfBirth, 16);
      const nationalityValidation = validateNationality(formData.nationality);
      const idValidation = validateIdPassport(formData.idPassportNumber);

      if (!firstNameValidation.isValid) errors.firstName = firstNameValidation.errors[0];
      if (!lastNameValidation.isValid) errors.lastName = lastNameValidation.errors[0];
      if (!dobValidation.isValid) errors.dateOfBirth = dobValidation.errors[0];
      if (!nationalityValidation.isValid) errors.nationality = nationalityValidation.errors[0];
      if (!idValidation.isValid) errors.idPassportNumber = idValidation.errors[0];
    }

    if (step === 2 && (userType === 'aspiring-model' || userType === 'professional-model')) { // Measurements
      const heightValidation = validateHeight(formData.height, formData.heightUnit);
      const bustValidation = validateMeasurement(formData.measurements.bust, 'Bust');
      const waistValidation = validateMeasurement(formData.measurements.waist, 'Waist');
      const hipsValidation = validateMeasurement(formData.measurements.hips, 'Hips');

      if (!heightValidation.isValid) errors.height = heightValidation.errors[0];
      if (!bustValidation.isValid) errors.bust = bustValidation.errors[0];
      if (!waistValidation.isValid) errors.waist = waistValidation.errors[0];
      if (!hipsValidation.isValid) errors.hips = hipsValidation.errors[0];
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  // GSAP Animations
  useEffect(() => {
    if (containerRef.current) {
      gsap.fromTo(containerRef.current.children, {
        opacity: 0,
        y: 20
      }, {
        opacity: 1,
        y: 0,
        duration: 0.5,
        stagger: 0.1,
        ease: 'power3.out',
        clearProps: 'transform' // Prevents layout shifts
      });
    }
  }, [step, userType]);
  const handleBack = () => {
    if (userType) {
      gsap.to(containerRef.current, {
        opacity: 0,
        y: 20,
        duration: 0.3,
        onComplete: () => {
          setUserType(null);
          gsap.fromTo(containerRef.current, {
            opacity: 0,
            y: 20
          }, {
            opacity: 1,
            y: 0,
            duration: 0.3
          });
        }
      });
    } else if (initialChoice) {
      gsap.to(containerRef.current, {
        opacity: 0,
        y: 20,
        duration: 0.3,
        onComplete: () => {
          setInitialChoice(null);
          gsap.fromTo(containerRef.current, {
            opacity: 0,
            y: 20
          }, {
            opacity: 1,
            y: 0,
            duration: 0.3
          });
        }
      });
    }
  };
  const handleNext = () => {
    if (!validateCurrentStep()) {
      return;
    }

    gsap.to(containerRef.current, {
      opacity: 0,
      x: -50,
      duration: 0.3,
      onComplete: () => {
        setStep(step + 1);
        setFormErrors({}); // Clear errors when moving to next step
        gsap.fromTo(containerRef.current, {
          opacity: 0,
          x: 50
        }, {
          opacity: 1,
          x: 0,
          duration: 0.3
        });
      }
    });
  };

  const handleComplete = () => {
    // Here you would typically save the data to your backend
    console.log('Onboarding completed:', {
      userType,
      formData,
      portfolioFiles
    });

    // Navigate to dashboard
    navigate('/dashboard');
  };
  const filteredCountries = countries.filter(country => country.toLowerCase().includes(countrySearch.toLowerCase()));

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        if (userType === 'aspiring-model' || userType === 'professional-model') {
          return renderMeasurementsStep();
        } else if (userType === 'talent') {
          return renderSpecialtyStep();
        }
        return renderDetailsStep();
      case 3:
        return renderPortfolioStep();
      case 4:
        return renderReviewStep();
      default:
        return renderBasicInfoStep();
    }
  };

  const renderBasicInfoStep = () => (
    <div className="space-y-8 w-full max-w-2xl">
      <h1 className="text-3xl font-semibold text-gray-900 text-center">
        Basic Information
      </h1>
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <FormInput
            label="First Name"
            value={formData.firstName}
            onChange={e => handleInputChange('firstName', e.target.value)}
            error={formErrors.firstName}
            placeholder="Enter your first name"
          />
          <FormInput
            label="Last Name"
            value={formData.lastName}
            onChange={e => handleInputChange('lastName', e.target.value)}
            error={formErrors.lastName}
            placeholder="Enter your last name"
          />
        </div>

        <FormInput
          label="ID / Passport Number"
          value={formData.idPassportNumber}
          onChange={e => handleInputChange('idPassportNumber', e.target.value)}
          error={formErrors.idPassportNumber}
          placeholder="Enter your ID or passport number"
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Gender
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(['male', 'female', 'non-binary', 'prefer-not-to-say'] as Gender[]).map(gender => (
              <button
                key={gender}
                onClick={() => handleInputChange('gender', gender)}
                className={`px-4 py-2 rounded-lg border-2 transition-colors ${
                  formData.gender === gender
                    ? 'border-black bg-black text-white'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {gender.charAt(0).toUpperCase() + gender.slice(1).replace('-', ' ')}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <FormInput
            label="Date of Birth"
            type="date"
            value={formData.dateOfBirth}
            onChange={e => handleInputChange('dateOfBirth', e.target.value)}
            error={formErrors.dateOfBirth}
          />
          <FormInput
            label="Nationality"
            value={formData.nationality}
            onChange={e => handleInputChange('nationality', e.target.value)}
            error={formErrors.nationality}
            placeholder="Enter your nationality"
          />
        </div>

        <button
          onClick={handleNext}
          className="w-full h-14 bg-black text-white rounded-full font-medium hover:bg-gray-900 transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  );

  const renderMeasurementsStep = () => (
    <div className="space-y-8 w-full max-w-2xl">
      <h1 className="text-3xl font-semibold text-gray-900 text-center">
        Measurements & Physical Details
      </h1>
      <div className="space-y-6">
        {renderHeightInput()}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Measurements (cm)
          </label>
          <div className="grid grid-cols-3 gap-4">
            <FormInput
              label=""
              placeholder="Bust"
              value={formData.measurements.bust}
              onChange={e => setFormData(prev => ({
                ...prev,
                measurements: { ...prev.measurements, bust: e.target.value }
              }))}
              error={formErrors.bust}
            />
            <FormInput
              label=""
              placeholder="Waist"
              value={formData.measurements.waist}
              onChange={e => setFormData(prev => ({
                ...prev,
                measurements: { ...prev.measurements, waist: e.target.value }
              }))}
              error={formErrors.waist}
            />
            <FormInput
              label=""
              placeholder="Hips"
              value={formData.measurements.hips}
              onChange={e => setFormData(prev => ({
                ...prev,
                measurements: { ...prev.measurements, hips: e.target.value }
              }))}
              error={formErrors.hips}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <FormInput
            label="Eye Color"
            value={formData.eyeColor}
            onChange={e => handleInputChange('eyeColor', e.target.value)}
            placeholder="e.g., Brown, Blue, Green"
          />
          <FormInput
            label="Hair Color"
            value={formData.hairColor}
            onChange={e => handleInputChange('hairColor', e.target.value)}
            placeholder="e.g., Blonde, Brunette, Black"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Experience Level
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(['none', 'some', 'experienced', 'professional'] as const).map(level => (
              <button
                key={level}
                onClick={() => handleInputChange('experience', level)}
                className={`px-4 py-2 rounded-lg border-2 transition-colors ${
                  formData.experience === level
                    ? 'border-black bg-black text-white'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </button>
            ))}
          </div>
        </div>

        <button
          onClick={handleNext}
          className="w-full h-14 bg-black text-white rounded-full font-medium hover:bg-gray-900 transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  );

  const handleUserTypeSelect = (type: UserType) => {
    gsap.to(containerRef.current, {
      opacity: 0,
      y: 20,
      duration: 0.3,
      ease: 'power2.in',
      onComplete: () => {
        setUserType(type);
        gsap.fromTo(containerRef.current, {
          opacity: 0,
          y: 20
        }, {
          opacity: 1,
          y: 0,
          duration: 0.5,
          ease: 'power2.out'
        });
      }
    });
  };
  const renderInitialChoice = () => <div className="space-y-8">
      <h1 className="text-4xl font-semibold text-gray-900 text-center">
        Welcome! Let's get started
      </h1>
      <p className="text-xl text-gray-600 text-center">I am a...</p>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 max-w-3xl mx-auto">
        <button onClick={() => handleInitialChoice('model-talent')} className="group relative p-8 border-2 border-gray-200 rounded-xl hover:border-black transition-all duration-300 bg-white">
          <div className="absolute top-8 right-8 text-gray-400 group-hover:text-black transition-colors">
            <User size={32} />
          </div>
          <div className="space-y-4 text-left">
            <h3 className="text-2xl font-medium text-gray-900">
              Model / Talent
            </h3>
            <p className="text-gray-600">
              Looking for castings and work opportunities
            </p>
          </div>
        </button>
        <button onClick={() => handleInitialChoice('agent-scout')} className="group relative p-8 border-2 border-gray-200 rounded-xl hover:border-black transition-all duration-300 bg-white">
          <div className="absolute top-8 right-8 text-gray-400 group-hover:text-black transition-colors">
            <Users size={32} />
          </div>
          <div className="space-y-4 text-left">
            <h3 className="text-2xl font-medium text-gray-900">
              Agency / Scout
            </h3>
            <p className="text-gray-600">Looking for models and talent</p>
          </div>
        </button>
      </div>
    </div>;
  const handleInitialChoice = (choice: InitialChoice) => {
    gsap.to(containerRef.current, {
      opacity: 0,
      y: 20,
      duration: 0.3,
      ease: 'power2.in',
      onComplete: () => {
        setInitialChoice(choice);
        gsap.fromTo(containerRef.current, {
          opacity: 0,
          y: 20
        }, {
          opacity: 1,
          y: 0,
          duration: 0.5,
          ease: 'power2.out'
        });
      }
    });
  };
  const renderUserTypeSelection = () => <div className="space-y-8">
      <h1 className="text-4xl font-semibold text-gray-900 text-center">
        Welcome! Let's get started
      </h1>
      <p className="text-xl text-gray-600 text-center">I am a...</p>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <button onClick={() => handleUserTypeSelect('aspiring-model')} className="group relative p-6 border-2 border-gray-200 rounded-xl hover:border-black transition-all duration-300 bg-white">
          <div className="absolute top-6 right-6 text-gray-400 group-hover:text-black transition-colors">
            <User size={24} />
          </div>
          <div className="space-y-4">
            <h3 className="text-xl font-medium text-gray-900">
              Aspiring Model
            </h3>
            <p className="text-gray-600">
              Start your modeling journey and discover opportunities
            </p>
          </div>
        </button>
        <button onClick={() => handleUserTypeSelect('professional-model')} className="group relative p-6 border-2 border-gray-200 rounded-xl hover:border-black transition-all duration-300 bg-white">
          <div className="absolute top-6 right-6 text-gray-400 group-hover:text-black transition-colors">
            <Briefcase size={24} />
          </div>
          <div className="space-y-4">
            <h3 className="text-xl font-medium text-gray-900">
              Professional Model
            </h3>
            <p className="text-gray-600">
              Expand your portfolio and find high-profile opportunities
            </p>
          </div>
        </button>
        <button onClick={() => handleUserTypeSelect('talent')} className="group relative p-6 border-2 border-gray-200 rounded-xl hover:border-black transition-all duration-300 bg-white">
          <div className="absolute top-6 right-6 text-gray-400 group-hover:text-black transition-colors">
            <Camera size={24} />
          </div>
          <div className="space-y-4">
            <h3 className="text-xl font-medium text-gray-900">
              Talent / Creative
            </h3>
            <p className="text-gray-600">
              Photographers, MUAs, and other creative professionals
            </p>
          </div>
        </button>
      </div>
    </div>;

  const renderPortfolioStep = () => (
    <div className="space-y-8 w-full max-w-4xl">
      <h1 className="text-3xl font-semibold text-gray-900 text-center">
        Build Your Portfolio
      </h1>
      <PortfolioUpload
        onFilesChange={setPortfolioFiles}
        className="w-full"
      />

      <div className="flex justify-between">
        <button
          onClick={() => setStep(step - 1)}
          className="px-6 py-3 border border-gray-300 rounded-full font-medium hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleNext}
          className="px-6 py-3 bg-black text-white rounded-full font-medium hover:bg-gray-900 transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  );

  const renderReviewStep = () => (
    <div className="space-y-8 w-full max-w-2xl">
      <h1 className="text-3xl font-semibold text-gray-900 text-center">
        Review Your Information
      </h1>

      <div className="bg-gray-50 rounded-lg p-6 space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Name:</span>
              <span className="ml-2">{formData.firstName} {formData.lastName}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Gender:</span>
              <span className="ml-2 capitalize">{formData.gender.replace('-', ' ')}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Date of Birth:</span>
              <span className="ml-2">{formData.dateOfBirth}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Nationality:</span>
              <span className="ml-2">{formData.nationality}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">ID/Passport:</span>
              <span className="ml-2">{formData.idPassportNumber}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Height:</span>
              <span className="ml-2">
                {formData.heightUnit === 'cm'
                  ? `${formData.height} cm`
                  : `${formData.heightFeet}'${formData.heightInches}"`
                }
              </span>
            </div>
          </div>
        </div>

        {(userType === 'aspiring-model' || userType === 'professional-model') && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Measurements</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Bust:</span>
                <span className="ml-2">{formData.measurements.bust} cm</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Waist:</span>
                <span className="ml-2">{formData.measurements.waist} cm</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Hips:</span>
                <span className="ml-2">{formData.measurements.hips} cm</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm mt-4">
              <div>
                <span className="font-medium text-gray-700">Eye Color:</span>
                <span className="ml-2">{formData.eyeColor}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Hair Color:</span>
                <span className="ml-2">{formData.hairColor}</span>
              </div>
            </div>
          </div>
        )}

        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio</h3>
          <p className="text-sm text-gray-600">
            {portfolioFiles.length} photo{portfolioFiles.length !== 1 ? 's' : ''} uploaded
          </p>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setStep(step - 1)}
          className="px-6 py-3 border border-gray-300 rounded-full font-medium hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleComplete}
          className="px-6 py-3 bg-black text-white rounded-full font-medium hover:bg-gray-900 transition-colors"
        >
          Complete Profile
        </button>
      </div>
    </div>
  );

  const renderHeightInput = () => <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">Height</label>
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          {heightUnit === 'cm' ? <input type="number" value={formData.height} onChange={e => handleInputChange('height', e.target.value)} className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent" placeholder="Height in cm" /> : <div className="grid grid-cols-2 gap-2">
              <input type="number" value={formData.heightFeet} onChange={e => handleInputChange('heightFeet', e.target.value)} className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent" placeholder="Feet" />
              <input type="number" value={formData.heightInches} onChange={e => handleInputChange('heightInches', e.target.value)} className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent" placeholder="Inches" />
            </div>}
        </div>
        <div className="flex border-2 border-gray-200 rounded-lg">
          <button onClick={() => setHeightUnit('cm')} className={`px-4 py-2 text-sm font-medium rounded-l-md ${heightUnit === 'cm' ? 'bg-black text-white' : 'text-gray-700 hover:bg-gray-50'}`}>
            cm
          </button>
          <button onClick={() => setHeightUnit('ft')} className={`px-4 py-2 text-sm font-medium rounded-r-md ${heightUnit === 'ft' ? 'bg-black text-white' : 'text-gray-700 hover:bg-gray-50'}`}>
            ft
          </button>
        </div>
      </div>
    </div>;
  return <div className="min-h-screen bg-white">
      {/* Hero image section */}
      <div className="w-full h-64 bg-black relative overflow-hidden">
        <img src="https://pub-e5ad4e77da0042ddbcb92671e6e5a061.r2.dev/black-sunglasses-style.webp" alt="Hero background" className="w-full h-full object-cover opacity-70" />
        <div className="absolute inset-0 flex items-center justify-between px-8"></div>
      </div>
      <div className="max-w-4xl mx-auto py-12 px-4">
        {/* Back Button */}
        <div className="flex justify-center mb-8">
          {initialChoice || userType ? <button onClick={handleBack} className="flex items-center text-gray-600 hover:text-black transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </button> : <button onClick={() => window.location.href = '/'} className="flex items-center text-gray-600 hover:text-black transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to login page
            </button>}
        </div>
        {/* Progress Bar */}
        {userType && <ProgressSteps steps={currentSteps} currentStep={step} />}
        {/* Content Container */}
        <div ref={containerRef} className="space-y-8 flex flex-col items-center">
          {(() => {
          if (!initialChoice) {
            return renderInitialChoice();
          }
          if (initialChoice === 'model-talent') {
            if (!userType) {
              return renderUserTypeSelection();
            }
            return renderStepContent();
          }
          return <div className="text-center">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Agency Onboarding Coming Soon
                </h2>
                <button onClick={() => setInitialChoice(null)} className="mt-4 text-black underline">
                  Go Back
                </button>
              </div>;
        })()}
        </div>
      </div>
    </div>;
}