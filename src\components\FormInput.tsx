import React, { forwardRef } from 'react';
import { Eye, EyeOff } from 'lucide-react';

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  showPasswordToggle?: boolean;
  showPassword?: boolean;
  onTogglePassword?: () => void;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  errorClassName?: string;
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  (
    {
      label,
      error,
      showPasswordToggle = false,
      showPassword = false,
      onTogglePassword,
      containerClassName = '',
      labelClassName = '',
      inputClassName = '',
      errorClassName = '',
      className,
      type,
      ...props
    },
    ref
  ) => {
    const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type;

    return (
      <div className={`space-y-2 ${containerClassName}`}>
        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>
          {label}
        </label>
        <div className="relative">
          <input
            ref={ref}
            type={inputType}
            className={`
              w-full px-4 py-2 border rounded-lg transition-colors
              focus:ring-2 focus:ring-black focus:border-transparent
              ${error 
                ? 'border-red-500 focus:ring-red-500' 
                : 'border-gray-300 hover:border-gray-400'
              }
              ${showPasswordToggle ? 'pr-10' : ''}
              ${inputClassName}
              ${className || ''}
            `}
            {...props}
          />
          {showPasswordToggle && onTogglePassword && (
            <button
              type="button"
              onClick={onTogglePassword}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          )}
        </div>
        {error && (
          <p className={`text-sm text-red-600 ${errorClassName}`}>
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormInput.displayName = 'FormInput';

// Dark theme variant for auth forms
interface AuthFormInputProps extends FormInputProps {
  darkTheme?: boolean;
}

export const AuthFormInput = forwardRef<HTMLInputElement, AuthFormInputProps>(
  (
    {
      label,
      error,
      showPasswordToggle = false,
      showPassword = false,
      onTogglePassword,
      darkTheme = true,
      containerClassName = '',
      labelClassName = '',
      inputClassName = '',
      errorClassName = '',
      className,
      type,
      ...props
    },
    ref
  ) => {
    const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type;

    if (darkTheme) {
      return (
        <div className={`space-y-2 ${containerClassName}`}>
          <label className={`block text-gray-200 text-sm font-normal ${labelClassName}`}>
            {label}
          </label>
          <div className="relative">
            <input
              ref={ref}
              type={inputType}
              className={`
                w-full bg-transparent text-zinc-500 text-lg font-normal 
                placeholder:text-zinc-500 placeholder:opacity-50 
                border-0 border-b border-zinc-400 pb-2 
                focus:outline-none focus:border-white transition-colors
                ${error ? 'border-red-500 focus:border-red-400' : ''}
                ${showPasswordToggle ? 'pr-8' : ''}
                ${inputClassName}
                ${className || ''}
              `}
              {...props}
            />
            {showPasswordToggle && onTogglePassword && (
              <button
                type="button"
                onClick={onTogglePassword}
                className="absolute right-0 top-1 text-neutral-600 hover:text-neutral-400 transition-colors"
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            )}
          </div>
          {error && (
            <p className={`text-sm text-red-400 ${errorClassName}`}>
              {error}
            </p>
          )}
        </div>
      );
    }

    // Light theme fallback
    return (
      <FormInput
        ref={ref}
        label={label}
        error={error}
        showPasswordToggle={showPasswordToggle}
        showPassword={showPassword}
        onTogglePassword={onTogglePassword}
        containerClassName={containerClassName}
        labelClassName={labelClassName}
        inputClassName={inputClassName}
        errorClassName={errorClassName}
        className={className}
        type={type}
        {...props}
      />
    );
  }
);

AuthFormInput.displayName = 'AuthFormInput';

// Select component
interface FormSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  error?: string;
  options: { value: string; label: string }[];
  containerClassName?: string;
  labelClassName?: string;
  selectClassName?: string;
  errorClassName?: string;
}

export const FormSelect = forwardRef<HTMLSelectElement, FormSelectProps>(
  (
    {
      label,
      error,
      options,
      containerClassName = '',
      labelClassName = '',
      selectClassName = '',
      errorClassName = '',
      className,
      ...props
    },
    ref
  ) => {
    return (
      <div className={`space-y-2 ${containerClassName}`}>
        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>
          {label}
        </label>
        <select
          ref={ref}
          className={`
            w-full px-4 py-2 border rounded-lg transition-colors
            focus:ring-2 focus:ring-black focus:border-transparent
            ${error 
              ? 'border-red-500 focus:ring-red-500' 
              : 'border-gray-300 hover:border-gray-400'
            }
            ${selectClassName}
            ${className || ''}
          `}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <p className={`text-sm text-red-600 ${errorClassName}`}>
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormSelect.displayName = 'FormSelect';

// Textarea component
interface FormTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  containerClassName?: string;
  labelClassName?: string;
  textareaClassName?: string;
  errorClassName?: string;
}

export const FormTextarea = forwardRef<HTMLTextAreaElement, FormTextareaProps>(
  (
    {
      label,
      error,
      containerClassName = '',
      labelClassName = '',
      textareaClassName = '',
      errorClassName = '',
      className,
      ...props
    },
    ref
  ) => {
    return (
      <div className={`space-y-2 ${containerClassName}`}>
        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>
          {label}
        </label>
        <textarea
          ref={ref}
          className={`
            w-full px-4 py-2 border rounded-lg transition-colors
            focus:ring-2 focus:ring-black focus:border-transparent
            resize-vertical
            ${error 
              ? 'border-red-500 focus:ring-red-500' 
              : 'border-gray-300 hover:border-gray-400'
            }
            ${textareaClassName}
            ${className || ''}
          `}
          {...props}
        />
        {error && (
          <p className={`text-sm text-red-600 ${errorClassName}`}>
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormTextarea.displayName = 'FormTextarea';
