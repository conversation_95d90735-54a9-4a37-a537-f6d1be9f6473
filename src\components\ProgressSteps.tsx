import React from 'react';
import { Check } from 'lucide-react';
interface ProgressStepsProps {
  steps: string[];
  currentStep: number;
}
export function ProgressSteps({
  steps,
  currentStep
}: ProgressStepsProps) {
  return <div className="w-full mb-8">
      <div className="relative flex justify-between">
        {steps.map((step, index) => <div key={index} className="flex flex-col items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 
                ${index < currentStep ? 'bg-black border-black text-white' : index === currentStep ? 'border-black text-black' : 'border-gray-300 text-gray-300'}`}>
              {index < currentStep ? <Check className="w-5 h-5" /> : <span>{index + 1}</span>}
            </div>
            <span className={`mt-2 text-sm ${index <= currentStep ? 'text-black' : 'text-gray-300'}`}>
              {step}
            </span>
          </div>)}
        <div className="absolute top-5 -z-10 h-[2px] bg-gray-200 w-full" style={{
        transform: 'translateY(-50%)'
      }} />
        <div className="absolute top-5 -z-10 h-[2px] bg-black transition-all duration-500 ease-in-out" style={{
        transform: 'translateY(-50%)',
        width: `${currentStep / (steps.length - 1) * 100}%`
      }} />
      </div>
    </div>;
}