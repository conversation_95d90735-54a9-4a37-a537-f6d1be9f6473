import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
}
export function AnimatedInput({
  label,
  error,
  ...props
}: AnimatedInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const labelRef = useRef<HTMLLabelElement>(null);
  useEffect(() => {
    const input = inputRef.current;
    const label = labelRef.current;
    if (input && label) {
      input.addEventListener('focus', () => {
        gsap.to(label, {
          y: -20,
          scale: 0.8,
          color: '#000',
          duration: 0.3,
          ease: 'power2.out'
        });
        gsap.to(input, {
          borderColor: '#000',
          duration: 0.3
        });
      });
      input.addEventListener('blur', () => {
        if (!input.value) {
          gsap.to(label, {
            y: 0,
            scale: 1,
            color: '#6B7280',
            duration: 0.3,
            ease: 'power2.out'
          });
        }
        gsap.to(input, {
          borderColor: '#E5E7EB',
          duration: 0.3
        });
      });
    }
    return () => {
      input?.removeEventListener('focus', () => {});
      input?.removeEventListener('blur', () => {});
    };
  }, []);
  return <div className="relative">
      <label ref={labelRef} className="absolute left-4 top-[50%] transform -translate-y-1/2 text-gray-500 pointer-events-none origin-left transition-all duration-200">
        {label}
      </label>
      <input ref={inputRef} {...props} className={`w-full px-4 py-3 border-2 rounded-lg focus:outline-none transition-colors ${error ? 'border-red-500' : 'border-gray-200'}`} />
      {error && <span className="text-red-500 text-sm mt-1 block">{error}</span>}
    </div>;
}