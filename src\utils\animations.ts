import gsap from 'gsap';
// Image fade in animation
export const imageEnterAnimation = (element: HTMLElement) => {
  gsap.fromTo(element, {
    opacity: 0,
    scale: 1.02
  }, {
    opacity: 1,
    scale: 1,
    duration: 0.4,
    ease: "power1.out",
    clearProps: "all"
  });
};
// Enhanced stagger animation for UI elements
export const fadeInStagger = (elements: HTMLElement[], delay: number = 0) => {
  const tl = gsap.timeline({
    delay
  });
  // Initial container fade in
  tl.from(elements[0], {
    opacity: 0,
    duration: 0.4,
    ease: "power2.out"
  })
  // Stagger all child elements
  .from(elements.slice(1), {
    opacity: 0,
    y: 15,
    duration: 0.5,
    stagger: 0.06,
    // Faster stagger for smoother flow
    ease: "power3.out",
    clearProps: "all" // Ensures clean up after animation
  }, "-=0.2"); // Slight overlap for smoother transition
};
export const inputFocusAnimation = (element: HTMLElement) => {
  gsap.to(element, {
    scale: 1.02,
    duration: 0.3,
    ease: "power2.out"
  });
};
export const inputBlurAnimation = (element: HTMLElement) => {
  gsap.to(element, {
    scale: 1,
    duration: 0.3,
    ease: "power2.out"
  });
};
export const buttonHoverAnimation = (element: HTMLElement) => {
  gsap.to(element, {
    scale: 1.05,
    duration: 0.3,
    ease: "power2.out"
  });
};
export const buttonLeaveAnimation = (element: HTMLElement) => {
  gsap.to(element, {
    scale: 1,
    duration: 0.3,
    ease: "power2.out"
  });
};
export const formTransitionAnimation = (exitingForm: HTMLElement, enteringForm: HTMLElement) => {
  const tl = gsap.timeline();
  tl.to(exitingForm, {
    opacity: 0,
    x: -50,
    duration: 0.4,
    ease: "power2.inOut"
  }).set(enteringForm, {
    x: 50
  }).to(enteringForm, {
    opacity: 1,
    x: 0,
    duration: 0.4,
    ease: "power2.inOut"
  });
};