import React, { useEffect, useState } from 'react';
import { imageEnterAnimation } from '../utils/animations';
interface ImageLoaderProps {
  src: string;
  alt: string;
  className?: string;
}
export function ImageLoader({
  src,
  alt,
  className = ''
}: ImageLoaderProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    // Preload image immediately
    const img = new Image();
    img.src = src;
    if (img.complete) {
      setIsLoaded(true);
    } else {
      img.onload = () => setIsLoaded(true);
    }
  }, [src]);
  return <div className="relative w-full h-full">
      <img src={src} alt={alt} className={`${className} absolute inset-0 transition-[opacity,transform] duration-[400ms] ease-in`} style={{
      opacity: isLoaded ? 1 : 0,
      transform: `scale(${isLoaded ? 1 : 1.02})`,
      willChange: 'opacity, transform'
    }} loading="eager" decoding="sync" />
    </div>;
}