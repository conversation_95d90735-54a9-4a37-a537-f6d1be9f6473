import React, { useState } from 'react';
import { LoginForm } from './components/LoginForm';
import { SignUpForm } from './components/SignUpForm';
import { UserOnboarding } from './components/UserOnboarding';
type View = 'login' | 'signup' | 'onboarding';
export function App() {
  const [currentView, setCurrentView] = useState<View>('login');
  return <div className="w-full min-h-screen bg-zinc-950">
      {currentView === 'onboarding' ? <UserOnboarding /> : currentView === 'login' ? <LoginForm onSwitchToSignUp={() => setCurrentView('signup')} onGuestLogin={() => setCurrentView('onboarding')} /> : <SignUpForm onSwitchToLogin={() => setCurrentView('login')} />}
    </div>;
}