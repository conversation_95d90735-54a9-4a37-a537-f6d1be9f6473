import React, { useEffect, useState, useRef } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { ImageLoader } from './ImageLoader';
import { AuthFormInput } from './FormInput';
import { fadeInStagger, inputFocusAnimation, inputBlurAnimation, buttonHoverAnimation, buttonLeaveAnimation } from '../utils/animations';
import { validateEmail, validateRequired, FormErrors } from '../utils/validation';
import { supabase } from '../lib/supabase';

export function LoginForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const formRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLHeadingElement>(null);
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const labelRefs = useRef<HTMLLabelElement[]>([]);
  const buttonRefs = useRef<HTMLButtonElement[]>([]);
  const textRefs = useRef<HTMLSpanElement[]>([]);
  const forgotPasswordRef = useRef<HTMLDivElement>(null);
  const signUpContainerRef = useRef<HTMLDivElement>(null);
  const loginImageUrl = 'https://pub-e5ad4e77da0042ddbcb92671e6e5a061.r2.dev/3MDLBW.webp';
  useEffect(() => {
    const preloadImage = new Image();
    preloadImage.src = loginImageUrl;
  }, []);
  useEffect(() => {
    if (formRef.current) {
      // Collect all elements that need to animate in the desired order
      const elements = [containerRef.current, headerRef.current, ...labelRefs.current, ...inputRefs.current, ...buttonRefs.current.filter(btn => btn !== null), forgotPasswordRef.current, signUpContainerRef.current, ...textRefs.current].filter(Boolean) as HTMLElement[];
      // Start stagger animation
      fadeInStagger(elements);
    }
  }, []);
  const handleInputFocus = (element: HTMLElement) => {
    inputFocusAnimation(element);
  };
  const handleInputBlur = (element: HTMLElement) => {
    inputBlurAnimation(element);
  };
  const handleButtonHover = (element: HTMLElement) => {
    buttonHoverAnimation(element);
  };
  const handleButtonLeave = (element: HTMLElement) => {
    buttonLeaveAnimation(element);
  };
  const validateForm = (): boolean => {
    const emailValidation = validateEmail(email);
    const passwordValidation = validateRequired(password, 'Password');

    const errors: FormErrors = {};

    if (!emailValidation.isValid) {
      errors.email = emailValidation.errors[0];
    }

    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0];
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setFormErrors({});

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const {
        data,
        error
      } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      if (error) throw error;
      // Handle successful login
      console.log('Logged in:', data);
      navigate('/dashboard');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      const {
        data,
        error
      } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          skipBrowserRedirect: false,
          redirectTo: window.location.origin
        }
      });
      if (error) {
        console.error('Google login error:', error);
        throw error;
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred with Google login');
    } finally {
      setIsLoading(false);
    }
  };
  return <div className="w-full min-h-screen flex bg-zinc-950 overflow-hidden" ref={formRef}>
      {/* Left side - Image */}
      <div className="hidden lg:block lg:w-3/5 xl:w-7/12 h-screen relative">
        <ImageLoader src={loginImageUrl} alt="Login background" className="w-full h-full object-cover" />
      </div>
      {/* Right side - Login Form */}
      <div className="w-full lg:w-2/5 xl:w-5/12 min-h-screen bg-black flex flex-col justify-center px-6 sm:px-12 lg:px-16 xl:px-24">
        <div className="w-full max-w-md mx-auto" ref={containerRef}>
          {/* Header */}
          <h2 ref={headerRef} className="text-zinc-400 text-base font-normal mb-12">
            Please fill in the fields below
          </h2>
          {/* Email Field */}
          <div className="mb-8">
            <AuthFormInput
              ref={el => inputRefs.current[0] = el as HTMLInputElement}
              label="Email"
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              onFocus={e => handleInputFocus(e.target)}
              onBlur={e => handleInputBlur(e.target)}
              placeholder="<EMAIL>"
              error={formErrors.email}
              darkTheme={true}
            />
          </div>
          {/* Password Field */}
          <div className="mb-8">
            <AuthFormInput
              ref={el => inputRefs.current[1] = el as HTMLInputElement}
              label="Password"
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              onFocus={e => handleInputFocus(e.target)}
              onBlur={e => handleInputBlur(e.target)}
              placeholder="Enter your password here"
              error={formErrors.password}
              showPasswordToggle={true}
              showPassword={showPassword}
              onTogglePassword={() => setShowPassword(!showPassword)}
              darkTheme={true}
            />
          </div>
          {/* Forgot Password */}
          <div className="mb-16" ref={forgotPasswordRef}>
            <span ref={el => textRefs.current[0] = el as HTMLSpanElement} className="text-stone-300 text-sm font-normal">
              Forgot your password.{' '}
            </span>
            <button ref={el => buttonRefs.current[3] = el as HTMLButtonElement} className="text-white text-sm font-bold underline hover:no-underline transition-all">
              Reset it here
            </button>
          </div>
          {/* Login Button */}
          {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
          <button ref={el => buttonRefs.current[0] = el as HTMLButtonElement} onClick={handleEmailLogin} disabled={isLoading} className="w-full h-14 bg-white rounded-full text-black text-base font-medium hover:bg-gray-100 transition-colors mb-6 disabled:opacity-50">
            {isLoading ? 'Logging in...' : 'Login now'}
          </button>
          {/* Google Login Button */}
          <button ref={el => buttonRefs.current[1] = el as HTMLButtonElement} onClick={handleGoogleLogin} disabled={isLoading} className="w-full h-14 rounded-full border-2 border-zinc-400 text-stone-300 text-base font-medium hover:border-zinc-300 hover:text-white transition-colors mb-16 disabled:opacity-50">
            Log in with Google
          </button>
          {/* Continue as Guest Button */}
          <button ref={el => buttonRefs.current[5] = el as HTMLButtonElement} onClick={() => navigate('/onboarding')} disabled={isLoading} className="w-full h-14 rounded-full border-2 border-zinc-400 text-stone-300 text-base font-medium hover:border-zinc-300 hover:text-white transition-colors mb-8">
            Continue as Guest
          </button>
          {/* Sign Up Link */}
          <div className="text-center" ref={signUpContainerRef}>
            <span ref={el => textRefs.current[1] = el as HTMLSpanElement} className="text-zinc-400 text-sm font-normal">
              Not registered{' '}
            </span>
            <button ref={el => buttonRefs.current[4] = el as HTMLButtonElement} onClick={() => navigate('/signup')} className="text-neutral-200 text-sm font-medium hover:text-white transition-colors">
              Sign up for an account
            </button>
          </div>
        </div>
      </div>
    </div>;
}