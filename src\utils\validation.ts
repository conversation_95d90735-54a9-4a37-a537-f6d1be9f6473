export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FormErrors {
  [key: string]: string;
}

// Email validation
export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please enter a valid email address');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Password validation
export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Confirm password validation
export const validateConfirmPassword = (password: string, confirmPassword: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!confirmPassword) {
    errors.push('Please confirm your password');
  } else if (password !== confirmPassword) {
    errors.push('Passwords do not match');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Name validation
export const validateName = (name: string, fieldName: string = 'Name'): ValidationResult => {
  const errors: string[] = [];
  
  if (!name) {
    errors.push(`${fieldName} is required`);
  } else if (name.length < 2) {
    errors.push(`${fieldName} must be at least 2 characters long`);
  } else if (!/^[a-zA-Z\s'-]+$/.test(name)) {
    errors.push(`${fieldName} can only contain letters, spaces, hyphens, and apostrophes`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Date validation
export const validateDate = (date: string, fieldName: string = 'Date'): ValidationResult => {
  const errors: string[] = [];
  
  if (!date) {
    errors.push(`${fieldName} is required`);
  } else {
    const dateObj = new Date(date);
    const today = new Date();
    
    if (isNaN(dateObj.getTime())) {
      errors.push(`Please enter a valid ${fieldName.toLowerCase()}`);
    } else if (dateObj > today) {
      errors.push(`${fieldName} cannot be in the future`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Age validation (for date of birth)
export const validateAge = (dateOfBirth: string, minAge: number = 16): ValidationResult => {
  const errors: string[] = [];
  
  if (!dateOfBirth) {
    errors.push('Date of birth is required');
  } else {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      // Haven't had birthday this year yet
    }
    
    if (age < minAge) {
      errors.push(`You must be at least ${minAge} years old`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Height validation
export const validateHeight = (height: string, unit: 'cm' | 'ft'): ValidationResult => {
  const errors: string[] = [];
  
  if (!height) {
    errors.push('Height is required');
  } else {
    const heightNum = parseFloat(height);
    
    if (isNaN(heightNum) || heightNum <= 0) {
      errors.push('Please enter a valid height');
    } else if (unit === 'cm') {
      if (heightNum < 120 || heightNum > 250) {
        errors.push('Height must be between 120cm and 250cm');
      }
    } else if (unit === 'ft') {
      if (heightNum < 4 || heightNum > 8) {
        errors.push('Height must be between 4ft and 8ft');
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Measurement validation
export const validateMeasurement = (measurement: string, fieldName: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!measurement) {
    errors.push(`${fieldName} is required`);
  } else {
    const measurementNum = parseFloat(measurement);
    
    if (isNaN(measurementNum) || measurementNum <= 0) {
      errors.push(`Please enter a valid ${fieldName.toLowerCase()}`);
    } else if (measurementNum < 50 || measurementNum > 200) {
      errors.push(`${fieldName} must be between 50cm and 200cm`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// ID/Passport validation
export const validateIdPassport = (idPassport: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!idPassport) {
    errors.push('ID/Passport number is required');
  } else if (idPassport.length < 5) {
    errors.push('ID/Passport number must be at least 5 characters long');
  } else if (!/^[a-zA-Z0-9]+$/.test(idPassport)) {
    errors.push('ID/Passport number can only contain letters and numbers');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Nationality validation
export const validateNationality = (nationality: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!nationality) {
    errors.push('Nationality is required');
  } else if (nationality.length < 2) {
    errors.push('Please enter a valid nationality');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Generic required field validation
export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!value || value.trim() === '') {
    errors.push(`${fieldName} is required`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Combine multiple validation results
export const combineValidationResults = (...results: ValidationResult[]): ValidationResult => {
  const allErrors = results.flatMap(result => result.errors);
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

// Convert validation results to form errors object
export const validationResultsToFormErrors = (validationMap: { [key: string]: ValidationResult }): FormErrors => {
  const formErrors: FormErrors = {};
  
  Object.entries(validationMap).forEach(([field, result]) => {
    if (!result.isValid && result.errors.length > 0) {
      formErrors[field] = result.errors[0]; // Take first error for each field
    }
  });
  
  return formErrors;
};
